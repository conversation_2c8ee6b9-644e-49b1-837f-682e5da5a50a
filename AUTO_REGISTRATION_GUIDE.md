# 🤖 HƯỚNG DẪN TỰ ĐỘNG ĐĂNG KÝ

## 🎯 Mục đích
Tính năng tự động đăng ký giúp bạn:
- Tự động điền form đăng ký trên website
- Tạo tài khoản và mật khẩu ngẫu nhiên
- Nhận thưởng nhiều lần với các tài khoản khác nhau
- Tiết kiệm thời gian đăng ký thủ công

## 🚀 Cách sử dụng

### Bước 1: <PERSON><PERSON><PERSON> bị
1. Khởi động Chrome Proxy Tool
2. <PERSON><PERSON><PERSON> proxy (hoặc không proxy)
3. Nhấn "🚀 Khởi động Chrome"

### Bước 2: Cấu hình đăng ký
1. **Bật tự động đăng ký**: ✅ Tick vào "Bật tự động đăng ký"

2. **Cấu hình thông tin**:
   - **Tà<PERSON> kho<PERSON>n**: <PERSON><PERSON> trống = tự động tạo (user123456)
   - **M<PERSON>t khẩu**: Đ<PERSON> trống = tự động tạo (8 ký tự ngẫu nhiên)
   - **Họ và tên**: Mặc định "TRAN HOANG AN" (có thể sửa)

### Bước 3: Thực hiện
1. Nhấn "🌐 Truy cập Website"
2. Chờ trang load xong
3. Nhấn "🤖 Tự động Đăng ký"

### Bước 4: Theo dõi
- Xem log để theo dõi quá trình
- Tool sẽ tự động:
  - Tìm form đăng ký
  - Điền thông tin
  - Click nút submit
  - Báo cáo kết quả

## 📋 Các chế độ hoạt động

### 🔄 Chế độ tự động hoàn toàn
- Để trống tất cả thông tin
- Tool tự tạo username và password
- Phù hợp để tạo nhiều tài khoản nhanh

### ✏️ Chế độ tùy chỉnh
- Nhập username và password riêng
- Kiểm soát thông tin đăng ký
- Phù hợp khi muốn tài khoản cụ thể

### 👤 Chế độ họ tên tùy chỉnh
- Thay đổi họ tên mặc định
- Sử dụng tên khác nhau cho mỗi lần đăng ký

## 🔍 Cách tool hoạt động

### 1. Phát hiện form đăng ký
Tool tìm kiếm form theo thứ tự:
- ID: register-form, registration-form, signup-form
- Class: register, registration, signup
- Form action: form[action*='register']
- Form đầu tiên trên trang

### 2. Tìm các trường input
**Username/Tài khoản**:
- `input[name*="username"]`
- `input[placeholder*="tài khoản"]`
- `input[type="text"]`

**Password/Mật khẩu**:
- `input[name*="password"]`
- `input[placeholder*="mật khẩu"]`
- `input[type="password"]`

**Confirm Password/Nhập lại MK**:
- `input[name*="confirm"]`
- `input[placeholder*="nhập lại"]`

**Full Name/Họ tên**:
- `input[name*="name"]`
- `input[placeholder*="họ tên"]`

### 3. Điền thông tin
- Xóa nội dung cũ
- Nhập từng ký tự với delay ngẫu nhiên (0.05-0.15s)
- Mô phỏng người dùng thật

### 4. Submit form
- Tìm nút submit: button[type="submit"], "Đăng ký", "Register"
- Scroll đến nút
- Click bằng JavaScript nếu cần

### 5. Kiểm tra kết quả
- Phân tích URL và nội dung trang
- Tìm từ khóa thành công: "success", "welcome", "dashboard"
- Tìm từ khóa lỗi: "error", "failed", "already exists"

## ⚙️ Tùy chọn nâng cao

### 🎲 Tạo username ngẫu nhiên
```
Format: user + 6 số ngẫu nhiên
Ví dụ: user123456, user789012
```

### 🔐 Tạo password ngẫu nhiên
```
Độ dài: 8 ký tự
Bao gồm: chữ cái (a-z, A-Z) + số (0-9)
Ví dụ: aB3dE7gH, X9mN2pQ5
```

### 🔄 Chu kỳ đăng ký
1. Đăng ký tài khoản → Nhận thưởng
2. Đóng Chrome → Xóa session/cookies
3. Khởi động Chrome mới → Proxy mới
4. Lặp lại quá trình

## 🚨 Lưu ý quan trọng

### ✅ Nên làm:
- Kiểm tra form đăng ký thủ công trước
- Sử dụng proxy khác nhau cho mỗi lần
- Đợi đủ thời gian giữa các lần đăng ký
- Lưu lại thông tin tài khoản đã tạo

### ❌ Không nên:
- Đăng ký quá nhanh liên tục
- Sử dụng cùng một proxy
- Bỏ qua kiểm tra kết quả
- Sử dụng thông tin cá nhân thật

### 🔒 Bảo mật:
- Tool chỉ hoạt động trên trình duyệt local
- Không lưu trữ thông tin nhạy cảm
- Tự động xóa session khi đóng Chrome

## 🔧 Xử lý sự cố

### ❌ Không tìm thấy form đăng ký
**Nguyên nhân**: Website có cấu trúc khác
**Giải pháp**:
- Kiểm tra trang có form đăng ký không
- Thử đăng ký thủ công trước
- Kiểm tra URL có đúng không

### ❌ Không điền được thông tin
**Nguyên nhân**: Selector không khớp
**Giải pháp**:
- Kiểm tra log để xem trường nào bị lỗi
- Thử điền thủ công để test
- Báo cáo để cải thiện tool

### ❌ Không click được nút submit
**Nguyên nhân**: Nút bị ẩn hoặc disabled
**Giải pháp**:
- Kiểm tra form đã điền đủ chưa
- Thử click thủ công
- Chờ thêm thời gian

### ❌ Đăng ký thất bại
**Nguyên nhân**: Website từ chối hoặc lỗi
**Giải pháp**:
- Kiểm tra thông báo lỗi trên trang
- Thử thông tin khác
- Đổi proxy và thử lại

## 📊 Tips tối ưu

### 🎯 Tăng tỷ lệ thành công:
- Sử dụng proxy chất lượng cao
- Đợi đủ thời gian load trang
- Kiểm tra form trước khi auto
- Sử dụng thông tin hợp lệ

### ⚡ Tăng tốc độ:
- Bật "Tắt hình ảnh"
- Sử dụng "Chế độ ẩn"
- Chuẩn bị sẵn thông tin

### 🔄 Tự động hóa:
- Bật "Bật tự động đăng ký"
- Để trống username/password
- Sử dụng chu kỳ đăng ký

## 📞 Hỗ trợ

### 📁 File log:
- `chrome_proxy_gui.log`: Log chi tiết quá trình
- Kiểm tra log khi có lỗi

### 🔍 Debug:
- Xem Developer Tools (F12)
- Kiểm tra Console tab
- Quan sát Network requests

### 💡 Cải thiện:
- Báo cáo website không tương thích
- Đề xuất cải thiện selector
- Chia sẻ kinh nghiệm sử dụng

**Lưu ý**: Tính năng này chỉ dành cho mục đích học tập và test. Hãy tuân thủ điều khoản sử dụng của website.
