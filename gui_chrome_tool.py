#!/usr/bin/env python3
"""
Chrome Proxy Tool với GUI
Tool truy cập web với giao diện đồ họa và proxy miễn phí
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import sys
import os
from pathlib import Path
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Import các module tự tạo
from proxy_manager import ProxyManager
from simple_chrome_tool import download_chromedriver_137
from auto_registration import AutoRegistration
from mass_registration import MassRegistration

class ChromeProxyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Chrome Proxy Tool - 13win Access")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Variables
        self.driver = None
        self.proxy_manager = ProxyManager()
        self.current_proxy = None
        self.is_running = False
        self.auto_registration = None
        self.mass_registration = None

        # Setup logging
        self.setup_logging()

        # Create GUI
        self.create_widgets()

        # Load initial data
        self.load_proxies_async()

    def setup_logging(self):
        """Thiết lập logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('chrome_proxy_gui.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)

    def create_widgets(self):
        """Tạo giao diện"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="Chrome Proxy Tool", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # URL Section
        url_frame = ttk.LabelFrame(main_frame, text="Website URL", padding="10")
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        url_frame.columnconfigure(1, weight=1)

        ttk.Label(url_frame, text="URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.url_var = tk.StringVar(value="https://www.13win16.com/?id=391111507")
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=50)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # Proxy Section
        proxy_frame = ttk.LabelFrame(main_frame, text="Proxy Settings", padding="10")
        proxy_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        proxy_frame.columnconfigure(1, weight=1)

        # Proxy mode
        ttk.Label(proxy_frame, text="Chế độ:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.proxy_mode = tk.StringVar(value="auto")
        proxy_mode_frame = ttk.Frame(proxy_frame)
        proxy_mode_frame.grid(row=0, column=1, sticky=tk.W)

        ttk.Radiobutton(proxy_mode_frame, text="Không proxy", variable=self.proxy_mode, value="none").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(proxy_mode_frame, text="Proxy tự động", variable=self.proxy_mode, value="auto").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(proxy_mode_frame, text="Proxy thủ công", variable=self.proxy_mode, value="manual").pack(side=tk.LEFT)

        # Manual proxy
        ttk.Label(proxy_frame, text="Proxy thủ công:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.manual_proxy_var = tk.StringVar()
        self.manual_proxy_entry = ttk.Entry(proxy_frame, textvariable=self.manual_proxy_var, width=30)
        self.manual_proxy_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 10))

        # Current proxy display
        ttk.Label(proxy_frame, text="Proxy hiện tại:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.current_proxy_var = tk.StringVar(value="Chưa có")
        ttk.Label(proxy_frame, textvariable=self.current_proxy_var, foreground="blue").grid(row=2, column=1, sticky=tk.W)

        # Proxy list
        proxy_list_frame = ttk.LabelFrame(main_frame, text="Danh sách Proxy miễn phí", padding="10")
        proxy_list_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        proxy_list_frame.columnconfigure(0, weight=1)
        proxy_list_frame.rowconfigure(1, weight=1)

        # Proxy list buttons
        proxy_buttons_frame = ttk.Frame(proxy_list_frame)
        proxy_buttons_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        self.refresh_proxy_btn = ttk.Button(proxy_buttons_frame, text="Làm mới Proxy", command=self.load_proxies_async)
        self.refresh_proxy_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.test_proxy_btn = ttk.Button(proxy_buttons_frame, text="Kiểm tra Proxy", command=self.test_selected_proxy)
        self.test_proxy_btn.pack(side=tk.LEFT)

        # Proxy treeview
        columns = ("proxy", "status", "ip", "speed")
        self.proxy_tree = ttk.Treeview(proxy_list_frame, columns=columns, show="headings", height=8)
        self.proxy_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure columns
        self.proxy_tree.heading("proxy", text="Proxy")
        self.proxy_tree.heading("status", text="Trạng thái")
        self.proxy_tree.heading("ip", text="IP")
        self.proxy_tree.heading("speed", text="Tốc độ")

        self.proxy_tree.column("proxy", width=120)
        self.proxy_tree.column("status", width=120)
        self.proxy_tree.column("ip", width=120)
        self.proxy_tree.column("speed", width=80)

        # Scrollbar for treeview
        proxy_scrollbar = ttk.Scrollbar(proxy_list_frame, orient=tk.VERTICAL, command=self.proxy_tree.yview)
        proxy_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.proxy_tree.configure(yscrollcommand=proxy_scrollbar.set)

        # Browser options
        options_frame = ttk.LabelFrame(main_frame, text="Tùy chọn Browser", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Headless mode
        self.headless_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="Chế độ ẩn (Headless)", variable=self.headless_var).pack(side=tk.LEFT, padx=(0, 20))

        # Disable images
        self.disable_images_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="Tắt hình ảnh", variable=self.disable_images_var).pack(side=tk.LEFT, padx=(0, 20))

        # User agent
        ttk.Label(options_frame, text="User Agent:").pack(side=tk.LEFT, padx=(0, 5))
        self.user_agent_var = tk.StringVar()
        user_agent_combo = ttk.Combobox(options_frame, textvariable=self.user_agent_var, width=30)
        user_agent_combo['values'] = [
            "Default",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
        ]
        user_agent_combo.set("Default")
        user_agent_combo.pack(side=tk.LEFT)

        # Auto Registration Section
        registration_frame = ttk.LabelFrame(main_frame, text="Tự động Đăng ký", padding="10")
        registration_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        registration_frame.columnconfigure(1, weight=1)

        # Enable auto registration
        self.auto_register_var = tk.BooleanVar()
        ttk.Checkbutton(registration_frame, text="Bật tự động đăng ký", variable=self.auto_register_var).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))

        # Username
        ttk.Label(registration_frame, text="Tài khoản:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(registration_frame, textvariable=self.username_var, width=20)
        self.username_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 10))
        ttk.Label(registration_frame, text="(Để trống = tự động tạo)", font=("Arial", 8)).grid(row=1, column=2, sticky=tk.W)

        # Password
        ttk.Label(registration_frame, text="Mật khẩu:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(registration_frame, textvariable=self.password_var, width=20, show="*")
        self.password_entry.grid(row=2, column=1, sticky=tk.W, padx=(0, 10))
        ttk.Label(registration_frame, text="(Để trống = tự động tạo)", font=("Arial", 8)).grid(row=2, column=2, sticky=tk.W)

        # Full name
        ttk.Label(registration_frame, text="Họ và tên:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10))
        self.fullname_var = tk.StringVar(value="TRAN HOANG AN")
        self.fullname_entry = ttk.Entry(registration_frame, textvariable=self.fullname_var, width=30)
        self.fullname_entry.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(0, 10))

        # Mass Registration Section
        mass_reg_frame = ttk.LabelFrame(registration_frame, text="Đăng ký Hàng loạt", padding="5")
        mass_reg_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        mass_reg_frame.columnconfigure(1, weight=1)

        # Number of accounts
        ttk.Label(mass_reg_frame, text="Số tài khoản:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.num_accounts_var = tk.StringVar(value="5")
        num_accounts_spinbox = ttk.Spinbox(mass_reg_frame, from_=1, to=100, textvariable=self.num_accounts_var, width=10)
        num_accounts_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))

        # Concurrent browsers
        ttk.Label(mass_reg_frame, text="Đồng thời:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.concurrent_var = tk.StringVar(value="2")
        concurrent_spinbox = ttk.Spinbox(mass_reg_frame, from_=1, to=5, textvariable=self.concurrent_var, width=5)
        concurrent_spinbox.grid(row=0, column=3, sticky=tk.W)

        # Headless mode for mass registration
        self.mass_headless_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(mass_reg_frame, text="Chế độ ẩn (khuyến nghị)", variable=self.mass_headless_var).grid(row=1, column=0, columnspan=4, sticky=tk.W, pady=(5, 0))

        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=6, column=0, columnspan=3, pady=(10, 0))

        self.start_btn = ttk.Button(control_frame, text="🚀 Khởi động Chrome", command=self.start_chrome)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="⏹️ Dừng Chrome", command=self.stop_chrome, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.navigate_btn = ttk.Button(control_frame, text="🌐 Truy cập Website", command=self.navigate_to_website, state=tk.DISABLED)
        self.navigate_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_log_btn = ttk.Button(control_frame, text="🗑️ Xóa Log", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.test_connection_btn = ttk.Button(control_frame, text="🔍 Test Kết nối", command=self.test_connection)
        self.test_connection_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.auto_register_btn = ttk.Button(control_frame, text="🤖 Tự động Đăng ký", command=self.start_auto_registration, state=tk.DISABLED)
        self.auto_register_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.mass_register_btn = ttk.Button(control_frame, text="🏭 Đăng ký Hàng loạt", command=self.start_mass_registration)
        self.mass_register_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_mass_btn = ttk.Button(control_frame, text="⏹️ Dừng Hàng loạt", command=self.stop_mass_registration, state=tk.DISABLED)
        self.stop_mass_btn.pack(side=tk.LEFT)

        # Status and log
        status_frame = ttk.LabelFrame(main_frame, text="Trạng thái & Log", padding="10")
        status_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)

        # Status
        self.status_var = tk.StringVar(value="Sẵn sàng")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, font=("Arial", 10, "bold"))
        status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Log text
        self.log_text = scrolledtext.ScrolledText(status_frame, height=8, width=80)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure main grid weights
        main_frame.rowconfigure(3, weight=1)
        main_frame.rowconfigure(7, weight=1)

    def clear_log(self):
        """Xóa log"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("Log đã được xóa")

    def test_connection(self):
        """Test kết nối internet và proxy"""
        def test():
            self.test_connection_btn.config(state=tk.DISABLED)

            try:
                # Test kết nối trực tiếp
                self.update_status("Đang test kết nối trực tiếp...")
                import requests
                response = requests.get('http://www.google.com', timeout=10)
                if response.status_code == 200:
                    self.update_status("✅ Kết nối trực tiếp: OK")
                else:
                    self.update_status("❌ Kết nối trực tiếp: Lỗi")

                # Test proxy nếu có chọn
                proxy = self.get_selected_proxy()
                if proxy and self.proxy_mode.get() != "none":
                    self.update_status(f"Đang test proxy {proxy}...")
                    proxy_dict = {
                        'http': f'http://{proxy}',
                        'https': f'http://{proxy}'
                    }

                    import time
                    start_time = time.time()
                    response = requests.get('http://www.google.com', proxies=proxy_dict, timeout=15)
                    end_time = time.time()

                    if response.status_code == 200:
                        speed = round(end_time - start_time, 2)
                        self.update_status(f"✅ Proxy {proxy}: OK ({speed}s)")
                    else:
                        self.update_status(f"❌ Proxy {proxy}: Lỗi kết nối")
                else:
                    self.update_status("ℹ️ Không có proxy được chọn để test")

            except Exception as e:
                self.update_status(f"❌ Lỗi test kết nối: {str(e)}")
            finally:
                self.test_connection_btn.config(state=tk.NORMAL)

        threading.Thread(target=test, daemon=True).start()

    def log_message(self, message):
        """Thêm message vào log"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # Log to file
        self.logger.info(message)

    def update_status(self, status):
        """Cập nhật trạng thái"""
        self.status_var.set(status)
        self.log_message(status)

    def load_proxies_async(self):
        """Load proxy list trong background"""
        def load_proxies():
            self.update_status("Đang tải danh sách proxy...")
            self.refresh_proxy_btn.config(state=tk.DISABLED)

            try:
                # Clear existing items
                for item in self.proxy_tree.get_children():
                    self.proxy_tree.delete(item)

                # Get proxies
                proxies = self.proxy_manager.get_working_proxies(20)

                # Update treeview
                for proxy in proxies:
                    self.proxy_tree.insert("", tk.END, values=(proxy, "Chưa kiểm tra", "Unknown", "N/A"))

                self.update_status(f"Đã tải {len(proxies)} proxy")

            except Exception as e:
                self.update_status(f"Lỗi khi tải proxy: {str(e)}")
            finally:
                self.refresh_proxy_btn.config(state=tk.NORMAL)

        threading.Thread(target=load_proxies, daemon=True).start()

    def test_selected_proxy(self):
        """Kiểm tra proxy được chọn"""
        selection = self.proxy_tree.selection()
        if not selection:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn một proxy để kiểm tra")
            return

        def test_proxy():
            self.test_proxy_btn.config(state=tk.DISABLED)

            for item in selection:
                proxy = self.proxy_tree.item(item)["values"][0]
                self.update_status(f"Đang kiểm tra proxy {proxy}...")

                # Update status to testing
                self.proxy_tree.item(item, values=(proxy, "Đang kiểm tra...", "Unknown", "Testing"))

                # Test proxy
                info = self.proxy_manager.get_proxy_info(proxy)

                # Update result
                speed_text = f"{info['speed']}s" if info['speed'] != 999 else "N/A"
                self.proxy_tree.item(item, values=(proxy, info["status"], info["ip"], speed_text))

                if info["status"] == "Working":
                    self.update_status(f"Proxy {proxy} hoạt động tốt - IP: {info['ip']}")
                else:
                    self.update_status(f"Proxy {proxy} không hoạt động")

            self.test_proxy_btn.config(state=tk.NORMAL)

        threading.Thread(target=test_proxy, daemon=True).start()

    def get_selected_proxy(self):
        """Lấy proxy được chọn hoặc tự động"""
        if self.proxy_mode.get() == "none":
            return None
        elif self.proxy_mode.get() == "manual":
            manual_proxy = self.manual_proxy_var.get().strip()
            return manual_proxy if manual_proxy else None
        else:  # auto mode
            selection = self.proxy_tree.selection()
            if selection:
                return self.proxy_tree.item(selection[0])["values"][0]
            else:
                # Get random working proxy
                return self.proxy_manager.get_random_proxy()

    def start_chrome(self):
        """Khởi động Chrome browser"""
        def start_browser():
            try:
                self.update_status("Đang khởi động Chrome...")
                self.start_btn.config(state=tk.DISABLED)

                # Get proxy
                proxy = self.get_selected_proxy()
                if proxy:
                    self.current_proxy = proxy
                    self.current_proxy_var.set(proxy)
                    self.update_status(f"Sử dụng proxy: {proxy}")
                else:
                    self.current_proxy = None
                    self.current_proxy_var.set("Không sử dụng proxy")
                    self.update_status("Không sử dụng proxy")

                # Setup Chrome options
                chrome_options = Options()

                # Basic options
                chrome_options.add_argument("--window-size=1920,1080")
                chrome_options.add_argument("--no-sandbox")
                chrome_options.add_argument("--disable-dev-shm-usage")
                chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # Headless mode
                if self.headless_var.get():
                    chrome_options.add_argument("--headless")
                    self.update_status("Chế độ ẩn được bật")

                # Disable images
                if self.disable_images_var.get():
                    prefs = {"profile.managed_default_content_settings.images": 2}
                    chrome_options.add_experimental_option("prefs", prefs)
                    self.update_status("Đã tắt hình ảnh")

                # User agent
                user_agent = self.user_agent_var.get()
                if user_agent and user_agent != "Default":
                    chrome_options.add_argument(f"--user-agent={user_agent}")
                    self.update_status(f"Sử dụng User Agent: {user_agent[:50]}...")

                # Add proxy if selected
                if self.current_proxy:
                    chrome_options.add_argument(f'--proxy-server=http://{self.current_proxy}')

                # Get ChromeDriver
                driver_path = download_chromedriver_137()
                if not driver_path:
                    raise Exception("Không thể tải ChromeDriver")

                # Start Chrome
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # Set timeouts
                self.driver.set_page_load_timeout(30)
                self.driver.implicitly_wait(10)

                # Hide automation indicators
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                self.is_running = True
                self.update_status("Chrome đã khởi động thành công!")

                # Update button states
                self.stop_btn.config(state=tk.NORMAL)
                self.navigate_btn.config(state=tk.NORMAL)
                self.auto_register_btn.config(state=tk.NORMAL)

                # Initialize auto registration
                self.auto_registration = AutoRegistration(self.driver)

            except Exception as e:
                self.update_status(f"Lỗi khởi động Chrome: {str(e)}")
                self.start_btn.config(state=tk.NORMAL)

        threading.Thread(target=start_browser, daemon=True).start()

    def stop_chrome(self):
        """Dừng Chrome browser"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None

            self.is_running = False
            self.update_status("Chrome đã được đóng")

            # Update button states
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.navigate_btn.config(state=tk.DISABLED)
            self.auto_register_btn.config(state=tk.DISABLED)

            # Reset auto registration
            self.auto_registration = None

        except Exception as e:
            self.update_status(f"Lỗi khi đóng Chrome: {str(e)}")

    def navigate_to_website(self):
        """Truy cập website với retry logic"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        def navigate():
            try:
                url = self.url_var.get().strip()
                if not url:
                    messagebox.showerror("Lỗi", "Vui lòng nhập URL!")
                    return

                self.update_status(f"Đang truy cập: {url}")
                self.navigate_btn.config(state=tk.DISABLED)

                # Thử truy cập với timeout ngắn hơn
                max_retries = 2
                for attempt in range(max_retries):
                    try:
                        if attempt > 0:
                            self.update_status(f"Thử lần {attempt + 1}...")

                        # Navigate to website
                        self.driver.get(url)

                        # Wait for page load với timeout ngắn hơn
                        WebDriverWait(self.driver, 20).until(
                            EC.presence_of_element_located((By.TAG_NAME, "body"))
                        )

                        current_url = self.driver.current_url
                        title = self.driver.title

                        self.update_status(f"✅ Truy cập thành công: {current_url}")
                        self.update_status(f"📄 Tiêu đề trang: {title}")

                        # Kiểm tra xem trang có load đúng không
                        if "error" in title.lower() or "not found" in title.lower():
                            self.update_status("⚠️ Trang có thể không load đúng")

                        return  # Thành công, thoát khỏi loop

                    except TimeoutException:
                        if attempt < max_retries - 1:
                            self.update_status(f"⏱️ Timeout lần {attempt + 1}, thử lại...")
                            continue
                        else:
                            self.update_status(f"❌ Timeout sau {max_retries} lần thử")

                            # Đề xuất thử không proxy
                            if self.current_proxy:
                                self.update_status("💡 Gợi ý: Thử chế độ 'Không proxy' hoặc proxy khác")

                    except WebDriverException as e:
                        error_msg = str(e)
                        if "net::ERR_PROXY_CONNECTION_FAILED" in error_msg:
                            self.update_status("❌ Lỗi kết nối proxy - Proxy không hoạt động")
                            self.update_status("💡 Gợi ý: Chọn proxy khác hoặc dùng 'Không proxy'")
                        elif "net::ERR_INTERNET_DISCONNECTED" in error_msg:
                            self.update_status("❌ Không có kết nối internet")
                        else:
                            self.update_status(f"❌ Lỗi WebDriver: {error_msg[:100]}...")
                        break

                # Nếu tất cả đều thất bại và đang dùng proxy
                if self.current_proxy:
                    result = messagebox.askyesno(
                        "Kết nối thất bại",
                        "Không thể truy cập với proxy hiện tại.\n\n"
                        "Bạn có muốn thử lại với chế độ 'Không proxy' không?"
                    )

                    if result:
                        self.proxy_mode.set("none")
                        self.update_status("🔄 Chuyển sang chế độ 'Không proxy' và khởi động lại...")
                        # Đóng Chrome hiện tại và khởi động lại
                        self.stop_chrome()
                        time.sleep(2)
                        self.start_chrome()

            except Exception as e:
                self.update_status(f"❌ Lỗi không xác định: {str(e)}")
            finally:
                self.navigate_btn.config(state=tk.NORMAL)

        threading.Thread(target=navigate, daemon=True).start()

    def start_auto_registration(self):
        """Bắt đầu quá trình đăng ký tự động"""
        if not self.driver or not self.is_running:
            messagebox.showerror("Lỗi", "Chrome chưa được khởi động!")
            return

        if not self.auto_registration:
            messagebox.showerror("Lỗi", "Auto registration chưa được khởi tạo!")
            return

        def auto_register():
            try:
                self.auto_register_btn.config(state=tk.DISABLED)
                self.update_status("🤖 Bắt đầu đăng ký tự động...")

                # Lấy thông tin từ form
                username = self.username_var.get().strip() or None
                password = self.password_var.get().strip() or None
                fullname = self.fullname_var.get().strip() or "TRAN HOANG AN"

                self.update_status(f"📝 Thông tin đăng ký:")
                self.update_status(f"   - Tài khoản: {username or 'Tự động tạo'}")
                self.update_status(f"   - Mật khẩu: {password or 'Tự động tạo'}")
                self.update_status(f"   - Họ tên: {fullname}")

                # Thực hiện đăng ký
                success = self.auto_registration.auto_register(
                    username=username,
                    password=password,
                    fullname=fullname
                )

                if success:
                    self.update_status("🎉 Đăng ký tự động thành công!")

                    # Hiển thị thông tin đã tạo
                    if not username or not password:
                        # Lấy thông tin đã tạo từ auto_registration
                        created_info = self.auto_registration.get_registration_info()
                        if created_info['username']:
                            self.username_var.set(created_info['username'])
                            self.update_status(f"📋 Tài khoản đã tạo: {created_info['username']}")
                        if created_info['password']:
                            self.password_var.set(created_info['password'])
                            self.update_status(f"🔑 Mật khẩu đã tạo: {created_info['password']}")

                    # Đề xuất hành động tiếp theo
                    result = messagebox.askyesno(
                        "Đăng ký thành công",
                        "Đăng ký tự động đã hoàn thành!\n\n"
                        "Bạn có muốn tiếp tục với trình duyệt mới để nhận thưởng lần nữa không?"
                    )

                    if result:
                        self.update_status("🔄 Chuẩn bị khởi động trình duyệt mới...")
                        # Đóng Chrome hiện tại và khởi động lại
                        self.stop_chrome()
                        time.sleep(3)
                        self.start_chrome()
                else:
                    self.update_status("❌ Đăng ký tự động thất bại")
                    self.update_status("💡 Gợi ý: Kiểm tra form đăng ký thủ công")

            except Exception as e:
                self.update_status(f"❌ Lỗi trong quá trình đăng ký: {str(e)}")
            finally:
                self.auto_register_btn.config(state=tk.NORMAL)

        threading.Thread(target=auto_register, daemon=True).start()

    def start_mass_registration(self):
        """Bắt đầu đăng ký hàng loạt"""
        try:
            num_accounts = int(self.num_accounts_var.get())
            concurrent = int(self.concurrent_var.get())
            headless = self.mass_headless_var.get()
            target_url = self.url_var.get().strip()

            if num_accounts < 1 or num_accounts > 100:
                messagebox.showerror("Lỗi", "Số tài khoản phải từ 1 đến 100!")
                return

            if concurrent < 1 or concurrent > 5:
                messagebox.showerror("Lỗi", "Số trình duyệt đồng thời phải từ 1 đến 5!")
                return

            if not target_url:
                messagebox.showerror("Lỗi", "Vui lòng nhập URL!")
                return

            # Xác nhận từ người dùng
            result = messagebox.askyesno(
                "Xác nhận đăng ký hàng loạt",
                f"Bạn có chắc muốn tạo {num_accounts} tài khoản?\n\n"
                f"Cài đặt:\n"
                f"- Số tài khoản: {num_accounts}\n"
                f"- Đồng thời: {concurrent} trình duyệt\n"
                f"- Chế độ: {'Ẩn' if headless else 'Hiển thị'}\n"
                f"- URL: {target_url}\n\n"
                f"Quá trình có thể mất {num_accounts * 2} - {num_accounts * 5} phút."
            )

            if not result:
                return

            # Khởi tạo mass registration
            self.mass_registration = MassRegistration(callback=self.update_status)

            # Update button states
            self.mass_register_btn.config(state=tk.DISABLED)
            self.stop_mass_btn.config(state=tk.NORMAL)

            def mass_register():
                try:
                    self.update_status(f"🏭 Bắt đầu đăng ký hàng loạt {num_accounts} tài khoản...")

                    accounts = self.mass_registration.mass_register(
                        num_accounts=num_accounts,
                        target_url=target_url,
                        max_concurrent=concurrent,
                        headless=headless
                    )

                    # Hiển thị kết quả
                    if accounts:
                        result_msg = f"🎉 Hoàn thành! Đã tạo {len(accounts)} tài khoản thành công!\n\n"
                        result_msg += "Danh sách tài khoản:\n"
                        for i, account in enumerate(accounts[:10], 1):  # Hiển thị tối đa 10 tài khoản
                            result_msg += f"{i}. {account['username']} / {account['password']}\n"

                        if len(accounts) > 10:
                            result_msg += f"... và {len(accounts) - 10} tài khoản khác\n"

                        result_msg += f"\n💾 Danh sách đầy đủ đã được lưu vào file accounts_*.txt"

                        messagebox.showinfo("Kết quả đăng ký hàng loạt", result_msg)
                    else:
                        messagebox.showwarning("Kết quả", "Không tạo được tài khoản nào!")

                except Exception as e:
                    self.update_status(f"❌ Lỗi đăng ký hàng loạt: {str(e)}")
                    messagebox.showerror("Lỗi", f"Lỗi trong quá trình đăng ký hàng loạt:\n{str(e)}")
                finally:
                    # Reset button states
                    self.mass_register_btn.config(state=tk.NORMAL)
                    self.stop_mass_btn.config(state=tk.DISABLED)
                    self.mass_registration = None

            threading.Thread(target=mass_register, daemon=True).start()

        except ValueError:
            messagebox.showerror("Lỗi", "Vui lòng nhập số hợp lệ!")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khởi động đăng ký hàng loạt: {str(e)}")

    def stop_mass_registration(self):
        """Dừng đăng ký hàng loạt"""
        if self.mass_registration:
            self.mass_registration.stop()
            self.update_status("⏹️ Đã yêu cầu dừng đăng ký hàng loạt...")

            # Reset button states
            self.mass_register_btn.config(state=tk.NORMAL)
            self.stop_mass_btn.config(state=tk.DISABLED)

    def navigate_to_website_with_auto_register(self):
        """Truy cập website và tự động đăng ký nếu được bật"""
        # Truy cập website trước
        self.navigate_to_website()

        # Nếu bật auto register, thực hiện sau khi load xong
        if self.auto_register_var.get():
            def delayed_register():
                time.sleep(5)  # Chờ trang load hoàn toàn
                self.start_auto_registration()

            threading.Thread(target=delayed_register, daemon=True).start()

    def on_closing(self):
        """Xử lý khi đóng ứng dụng"""
        if self.driver:
            self.stop_chrome()
        self.root.destroy()


def main():
    """Main function"""
    root = tk.Tk()
    app = ChromeProxyGUI(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start GUI
    root.mainloop()


if __name__ == "__main__":
    main()
