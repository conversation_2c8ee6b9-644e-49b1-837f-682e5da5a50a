#!/usr/bin/env python3
"""
Khởi chạy Simple Auto Register Tool
"""

import sys
import os

def main():
    """Main function"""
    print("=" * 50)
    print("Simple Auto Register Tool")
    print("Tool tự động đăng ký đơn giản")
    print("=" * 50)
    print()
    
    try:
        from simple_auto_register import main as simple_main
        simple_main()
        
    except ImportError as e:
        print(f"Lỗi import: {e}")
        print("<PERSON>ui lòng cài đặt thư viện cần thiết:")
        print("pip install selenium")
        
    except Exception as e:
        print(f"Lỗi khởi động: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
