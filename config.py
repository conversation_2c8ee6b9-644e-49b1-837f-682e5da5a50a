"""
Configuration file for Chrome proxy tool
"""

# Proxy configuration
PROXY_CONFIG = {
    # HTTP/HTTPS proxy
    'http_proxy': None,  # Example: 'http://proxy.example.com:8080'
    'https_proxy': None,  # Example: 'https://proxy.example.com:8080'
    
    # SOCKS proxy
    'socks_proxy': None,  # Example: 'socks5://proxy.example.com:1080'
    
    # Proxy authentication (if required)
    'username': None,
    'password': None,
}

# Target website
TARGET_URL = "https://www.13win16.com/?id=391111507"

# Chrome options
CHROME_OPTIONS = {
    'headless': False,  # Set to True to run in headless mode
    'disable_images': False,  # Set to True to disable image loading for faster browsing
    'window_size': (1920, 1080),
    'user_agent': None,  # Custom user agent if needed
}

# Timeouts (in seconds)
TIMEOUTS = {
    'page_load': 30,
    'implicit_wait': 10,
}
