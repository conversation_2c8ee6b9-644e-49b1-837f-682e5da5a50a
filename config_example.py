"""
Example configuration for Chrome proxy tool
Copy this to config.py and modify as needed
"""

# Proxy configuration - MODIFY THESE VALUES
PROXY_CONFIG = {
    # HTTP/HTTPS proxy - uncomment and set your proxy
    'http_proxy': None,  # 'http://your-proxy.com:8080'
    'https_proxy': None,  # 'http://your-proxy.com:8080'
    
    # SOCKS proxy - uncomment if using SOCKS
    'socks_proxy': None,  # 'socks5://your-proxy.com:1080'
    
    # Proxy authentication - set if required
    'username': None,  # 'your_username'
    'password': None,  # 'your_password'
}

# Target website
TARGET_URL = "https://www.13win16.com/?id=391111507"

# Chrome options
CHROME_OPTIONS = {
    'headless': False,  # Set to True to run in headless mode
    'disable_images': False,  # Set to True to disable image loading
    'window_size': (1920, 1080),
    'user_agent': None,  # Custom user agent if needed
}

# Timeouts (in seconds)
TIMEOUTS = {
    'page_load': 30,
    'implicit_wait': 10,
}
