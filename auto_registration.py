#!/usr/bin/env python3
"""
Auto Registration Module
Tự động đăng ký tài khoản trên website
"""

import time
import random
import string
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

logger = logging.getLogger(__name__)

class AutoRegistration:
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 15)

    def generate_random_username(self, prefix="user"):
        """Tạo username ngẫu nhiên"""
        random_suffix = ''.join(random.choices(string.digits, k=6))
        return f"{prefix}{random_suffix}"

    def generate_random_password(self, length=8):
        """Tạo password ngẫu nhiên"""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choices(characters, k=length))

    def wait_and_find_element(self, by, value, timeout=15):
        """Chờ và tìm element"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            logger.error(f"Không tìm thấy element: {by}={value}")
            return None

    def safe_click(self, element):
        """Click an toàn"""
        try:
            # Scroll to element
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # Try normal click first
            element.click()
            return True
        except Exception as e:
            try:
                # Try JavaScript click as fallback
                self.driver.execute_script("arguments[0].click();", element)
                return True
            except Exception as e2:
                logger.error(f"Không thể click element: {e2}")
                return False

    def safe_send_keys(self, element, text, clear_first=True):
        """Nhập text an toàn"""
        try:
            if clear_first:
                element.clear()

            # Nhập từng ký tự với delay ngẫu nhiên
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            return True
        except Exception as e:
            logger.error(f"Không thể nhập text: {e}")
            return False

    def find_registration_form(self):
        """Tìm form đăng ký"""
        # Các selector có thể có cho form đăng ký
        possible_selectors = [
            # By ID
            "register-form", "registration-form", "signup-form", "reg-form",
            # By class
            "register", "registration", "signup", "sign-up",
            # By form action
            "form[action*='register']", "form[action*='signup']",
            # Generic form
            "form"
        ]

        for selector in possible_selectors:
            try:
                if selector.startswith("form["):
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                else:
                    # Try ID first, then class
                    try:
                        element = self.driver.find_element(By.ID, selector)
                    except NoSuchElementException:
                        element = self.driver.find_element(By.CLASS_NAME, selector)

                if element:
                    logger.info(f"Tìm thấy form đăng ký: {selector}")
                    return element
            except NoSuchElementException:
                continue

        logger.warning("Không tìm thấy form đăng ký cụ thể, sử dụng form đầu tiên")
        try:
            return self.driver.find_element(By.TAG_NAME, "form")
        except NoSuchElementException:
            return None

    def find_input_field(self, field_type):
        """Tìm input field theo loại"""
        selectors = {
            'username': [
                'input[name*="username"]', 'input[name*="user"]', 'input[name*="login"]',
                'input[id*="username"]', 'input[id*="user"]', 'input[id*="login"]',
                'input[placeholder*="tài khoản"]', 'input[placeholder*="username"]',
                'input[type="text"]'
            ],
            'password': [
                'input[name*="password"]', 'input[name*="pass"]', 'input[name*="pwd"]',
                'input[id*="password"]', 'input[id*="pass"]', 'input[id*="pwd"]',
                'input[placeholder*="mật khẩu"]', 'input[placeholder*="password"]',
                'input[type="password"]'
            ],
            'confirm_password': [
                'input[name*="confirm"]', 'input[name*="repeat"]', 'input[name*="retype"]',
                'input[id*="confirm"]', 'input[id*="repeat"]', 'input[id*="retype"]',
                'input[placeholder*="nhập lại"]', 'input[placeholder*="confirm"]'
            ],
            'fullname': [
                'input[name*="name"]', 'input[name*="fullname"]', 'input[name*="full_name"]',
                'input[id*="name"]', 'input[id*="fullname"]', 'input[id*="full_name"]',
                'input[placeholder*="họ tên"]', 'input[placeholder*="name"]'
            ],
            'email': [
                'input[name*="email"]', 'input[name*="mail"]',
                'input[id*="email"]', 'input[id*="mail"]',
                'input[placeholder*="email"]', 'input[type="email"]'
            ]
        }

        if field_type not in selectors:
            return None

        for selector in selectors[field_type]:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Nếu có nhiều element, chọn element phù hợp nhất
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            return element
            except Exception:
                continue

        return None

    def find_submit_button(self):
        """Tìm nút submit"""
        selectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:contains("Đăng ký")',
            'button:contains("Register")',
            'button:contains("Sign up")',
            'button:contains("Submit")',
            '.btn-submit', '.submit-btn', '.register-btn',
            'button', 'input[type="button"]'
        ]

        for selector in selectors:
            try:
                if ':contains(' in selector:
                    # Use XPath for text content
                    text_content = selector.split(':contains(')[1].split(')')[0].strip('"')
                    xpath = f"//button[contains(text(), '{text_content}')]"
                    elements = self.driver.find_elements(By.XPATH, xpath)
                else:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        text = element.text.lower()
                        if any(keyword in text for keyword in ['đăng ký', 'register', 'sign up', 'submit']):
                            return element
            except Exception:
                continue

        # Fallback: tìm button cuối cùng trong form
        try:
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            if buttons:
                return buttons[-1]
        except Exception:
            pass

        return None

    def auto_register(self, username=None, password=None, fullname="TRAN HOANG AN"):
        """Tự động đăng ký tài khoản"""
        try:
            logger.info("Bắt đầu quá trình đăng ký tự động...")

            # Tạo thông tin ngẫu nhiên nếu không được cung cấp
            if not username:
                username = self.generate_random_username()
            if not password:
                password = self.generate_random_password()

            # Lưu thông tin để có thể truy xuất sau
            self.last_username = username
            self.last_password = password
            self.last_fullname = fullname

            logger.info(f"Thông tin đăng ký: Username={username}, Password={password}, Fullname={fullname}")

            # Chờ trang load
            time.sleep(3)

            # Tìm form đăng ký
            form = self.find_registration_form()
            if not form:
                logger.error("Không tìm thấy form đăng ký")
                return False

            # Tìm và điền các trường
            fields_to_fill = [
                ('username', username),
                ('fullname', fullname),
                ('password', password),
                ('confirm_password', password)
            ]

            filled_fields = 0
            for field_type, value in fields_to_fill:
                field = self.find_input_field(field_type)
                if field:
                    logger.info(f"Điền {field_type}: {value}")
                    if self.safe_send_keys(field, value):
                        filled_fields += 1
                        time.sleep(random.uniform(0.5, 1.5))
                    else:
                        logger.warning(f"Không thể điền {field_type}")
                else:
                    logger.warning(f"Không tìm thấy trường {field_type}")

            if filled_fields == 0:
                logger.error("Không thể điền bất kỳ trường nào")
                return False

            logger.info(f"Đã điền {filled_fields}/{len(fields_to_fill)} trường")

            # Tìm và click nút submit
            submit_btn = self.find_submit_button()
            if submit_btn:
                logger.info("Tìm thấy nút submit, chuẩn bị click...")
                time.sleep(2)  # Chờ một chút trước khi submit

                if self.safe_click(submit_btn):
                    logger.info("✅ Đã click nút đăng ký")

                    # Chờ và kiểm tra kết quả
                    time.sleep(5)

                    # Kiểm tra URL hoặc thông báo thành công
                    current_url = self.driver.current_url
                    page_source = self.driver.page_source.lower()

                    success_indicators = [
                        'success', 'thành công', 'welcome', 'chào mừng',
                        'dashboard', 'profile', 'account'
                    ]

                    error_indicators = [
                        'error', 'lỗi', 'failed', 'thất bại', 'invalid',
                        'already exists', 'đã tồn tại'
                    ]

                    if any(indicator in page_source for indicator in success_indicators):
                        logger.info("🎉 Đăng ký thành công!")
                        return True
                    elif any(indicator in page_source for indicator in error_indicators):
                        logger.warning("⚠️ Có thể có lỗi trong quá trình đăng ký")
                        return False
                    else:
                        logger.info("✅ Đã submit form, kiểm tra thủ công kết quả")
                        return True
                else:
                    logger.error("Không thể click nút submit")
                    return False
            else:
                logger.error("Không tìm thấy nút submit")
                return False

        except Exception as e:
            logger.error(f"Lỗi trong quá trình đăng ký: {str(e)}")
            return False

    def get_registration_info(self):
        """Lấy thông tin đăng ký đã tạo"""
        return {
            'username': getattr(self, 'last_username', None),
            'password': getattr(self, 'last_password', None),
            'fullname': getattr(self, 'last_fullname', None)
        }
