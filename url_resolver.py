#!/usr/bin/env python3
"""
URL Resolver - Tìm URL hoạt động cho website
"""

import requests
import time
import random
from urllib.parse import urlparse
import logging

logger = logging.getLogger(__name__)

class URLResolver:
    def __init__(self):
        self.working_urls = []
        self.failed_urls = []
        
    def generate_alternative_urls(self, base_url):
        """Tạo danh sách URL thay thế"""
        parsed = urlparse(base_url)
        domain_parts = parsed.netloc.split('.')
        
        alternatives = [
            base_url,  # URL gốc
        ]
        
        # Thử các biến thể domain
        if '13win16' in parsed.netloc:
            alternatives.extend([
                base_url.replace('13win16', '13win'),
                base_url.replace('13win16', '13win17'),
                base_url.replace('13win16', '13win18'),
                base_url.replace('13win16', '13win19'),
                base_url.replace('13win16', '13win20'),
                base_url.replace('www.13win16', '13win16'),
                base_url.replace('www.13win16', '13win'),
                base_url.replace('.com', '.net'),
                base_url.replace('.com', '.org'),
                base_url.replace('.com', '.info'),
                base_url.replace('.com', '.co'),
            ])
        
        # Thử các subdomain khác
        if 'www.' in parsed.netloc:
            alternatives.append(base_url.replace('www.', ''))
            alternatives.append(base_url.replace('www.', 'm.'))
            alternatives.append(base_url.replace('www.', 'mobile.'))
        else:
            alternatives.append(base_url.replace('://', '://www.'))
            alternatives.append(base_url.replace('://', '://m.'))
        
        # Thử HTTP thay vì HTTPS
        if base_url.startswith('https://'):
            alternatives.append(base_url.replace('https://', 'http://'))
        
        # Loại bỏ trùng lặp
        return list(set(alternatives))
    
    def test_url_accessibility(self, url, timeout=10, use_proxy=None):
        """Kiểm tra URL có truy cập được không"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            proxies = None
            if use_proxy:
                proxies = {
                    'http': f'http://{use_proxy}',
                    'https': f'http://{use_proxy}'
                }
            
            response = requests.get(
                url, 
                headers=headers, 
                timeout=timeout, 
                proxies=proxies,
                allow_redirects=True,
                verify=False  # Bỏ qua SSL verification
            )
            
            # Kiểm tra response
            if response.status_code == 200:
                content = response.text.lower()
                # Kiểm tra nội dung có liên quan không
                if any(keyword in content for keyword in ['13win', 'casino', 'game', 'login', 'register']):
                    return {
                        'url': url,
                        'status': 'working',
                        'status_code': response.status_code,
                        'final_url': response.url,
                        'response_time': response.elapsed.total_seconds(),
                        'content_length': len(response.content)
                    }
            
            return {
                'url': url,
                'status': 'failed',
                'status_code': response.status_code,
                'error': f'HTTP {response.status_code}'
            }
            
        except requests.exceptions.Timeout:
            return {
                'url': url,
                'status': 'timeout',
                'error': 'Request timeout'
            }
        except requests.exceptions.ConnectionError:
            return {
                'url': url,
                'status': 'connection_error',
                'error': 'Connection failed'
            }
        except Exception as e:
            return {
                'url': url,
                'status': 'error',
                'error': str(e)
            }
    
    def find_working_urls(self, base_url, max_urls=5, use_proxy=None):
        """Tìm các URL hoạt động"""
        logger.info(f"Đang tìm URL hoạt động cho: {base_url}")
        
        # Tạo danh sách URL thay thế
        alternative_urls = self.generate_alternative_urls(base_url)
        logger.info(f"Sẽ kiểm tra {len(alternative_urls)} URL thay thế")
        
        working_urls = []
        failed_urls = []
        
        for i, url in enumerate(alternative_urls):
            if len(working_urls) >= max_urls:
                break
                
            logger.info(f"Kiểm tra URL {i+1}/{len(alternative_urls)}: {url}")
            
            result = self.test_url_accessibility(url, timeout=15, use_proxy=use_proxy)
            
            if result['status'] == 'working':
                working_urls.append(result)
                logger.info(f"✅ URL hoạt động: {url} (thời gian: {result.get('response_time', 0):.2f}s)")
            else:
                failed_urls.append(result)
                logger.warning(f"❌ URL không hoạt động: {url} - {result.get('error', 'Unknown error')}")
            
            # Delay nhỏ giữa các request
            time.sleep(random.uniform(1, 3))
        
        self.working_urls = working_urls
        self.failed_urls = failed_urls
        
        logger.info(f"Kết quả: {len(working_urls)} URL hoạt động, {len(failed_urls)} URL thất bại")
        
        return working_urls
    
    def get_best_url(self, base_url, use_proxy=None):
        """Lấy URL tốt nhất (nhanh nhất)"""
        working_urls = self.find_working_urls(base_url, max_urls=10, use_proxy=use_proxy)
        
        if not working_urls:
            return None
        
        # Sắp xếp theo thời gian phản hồi
        working_urls.sort(key=lambda x: x.get('response_time', 999))
        
        best_url = working_urls[0]
        logger.info(f"URL tốt nhất: {best_url['url']} (thời gian: {best_url.get('response_time', 0):.2f}s)")
        
        return best_url
    
    def test_with_multiple_proxies(self, base_url, proxies):
        """Test URL với nhiều proxy khác nhau"""
        results = {}
        
        for proxy in proxies:
            logger.info(f"Test với proxy: {proxy}")
            
            try:
                working_urls = self.find_working_urls(base_url, max_urls=3, use_proxy=proxy)
                results[proxy] = {
                    'status': 'success',
                    'working_urls': working_urls,
                    'count': len(working_urls)
                }
                
                if working_urls:
                    logger.info(f"✅ Proxy {proxy}: Tìm thấy {len(working_urls)} URL hoạt động")
                else:
                    logger.warning(f"⚠️ Proxy {proxy}: Không tìm thấy URL nào hoạt động")
                    
            except Exception as e:
                results[proxy] = {
                    'status': 'error',
                    'error': str(e),
                    'working_urls': [],
                    'count': 0
                }
                logger.error(f"❌ Proxy {proxy}: Lỗi - {str(e)}")
            
            # Delay giữa các proxy
            time.sleep(2)
        
        return results


def main():
    """Test URL resolver"""
    logging.basicConfig(level=logging.INFO)
    
    resolver = URLResolver()
    
    # Test tìm URL hoạt động
    base_url = "https://www.13win16.com/?id=391111507"
    
    print("🔍 Đang tìm URL hoạt động...")
    working_urls = resolver.find_working_urls(base_url)
    
    if working_urls:
        print(f"\n✅ Tìm thấy {len(working_urls)} URL hoạt động:")
        for i, url_info in enumerate(working_urls, 1):
            print(f"{i}. {url_info['url']}")
            print(f"   - Thời gian phản hồi: {url_info.get('response_time', 0):.2f}s")
            print(f"   - Kích thước: {url_info.get('content_length', 0)} bytes")
            if url_info['url'] != url_info.get('final_url', url_info['url']):
                print(f"   - Redirect đến: {url_info.get('final_url')}")
    else:
        print("❌ Không tìm thấy URL nào hoạt động")
        print("\nURL đã thử:")
        for url_info in resolver.failed_urls:
            print(f"- {url_info['url']}: {url_info.get('error', 'Unknown error')}")


if __name__ == "__main__":
    main()
