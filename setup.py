#!/usr/bin/env python3
"""
Setup script for Chrome Proxy Tool
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def check_chrome():
    """Check if Chrome is installed"""
    print("Checking Chrome installation...")
    
    # Common Chrome paths
    chrome_paths = [
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✓ Chrome found at: {path}")
            return True
    
    print("⚠ Chrome not found in common locations")
    print("Please make sure Google Chrome is installed")
    return False

def create_example_config():
    """Create an example configuration file"""
    print("Creating example configuration...")
    
    example_config = '''"""
Example configuration for Chrome proxy tool
Copy this to config.py and modify as needed
"""

# Proxy configuration - MODIFY THESE VALUES
PROXY_CONFIG = {
    # HTTP/HTTPS proxy - uncomment and set your proxy
    'http_proxy': None,  # 'http://your-proxy.com:8080'
    'https_proxy': None,  # 'http://your-proxy.com:8080'
    
    # SOCKS proxy - uncomment if using SOCKS
    'socks_proxy': None,  # 'socks5://your-proxy.com:1080'
    
    # Proxy authentication - set if required
    'username': None,  # 'your_username'
    'password': None,  # 'your_password'
}

# Target website
TARGET_URL = "https://www.13win16.com/?id=391111507"

# Chrome options
CHROME_OPTIONS = {
    'headless': False,  # Set to True to run in headless mode
    'disable_images': False,  # Set to True to disable image loading
    'window_size': (1920, 1080),
    'user_agent': None,  # Custom user agent if needed
}

# Timeouts (in seconds)
TIMEOUTS = {
    'page_load': 30,
    'implicit_wait': 10,
}
'''
    
    try:
        with open('config_example.py', 'w', encoding='utf-8') as f:
            f.write(example_config)
        print("✓ Example configuration created as config_example.py")
        return True
    except Exception as e:
        print(f"✗ Failed to create example config: {e}")
        return False

def main():
    """Main setup function"""
    print("Chrome Proxy Tool Setup")
    print("=" * 30)
    
    success = True
    
    # Install requirements
    if not install_requirements():
        success = False
    
    # Check Chrome
    check_chrome()
    
    # Create example config
    if not create_example_config():
        success = False
    
    print("\n" + "=" * 30)
    if success:
        print("✓ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Copy config_example.py to config.py")
        print("2. Edit config.py with your proxy settings")
        print("3. Run: python main.py")
    else:
        print("✗ Setup completed with errors")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
