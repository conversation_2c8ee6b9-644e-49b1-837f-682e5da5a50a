# 🏭 HƯỚNG DẪN ĐĂNG KÝ HÀNG LOẠT

## 🎯 Mục đích
Tính năng đăng ký hàng loạt giúp bạn:
- Tạo nhiều tài khoản cùng lúc (1-100 tài khoản)
- Mỗi tài khoản sử dụng trình duyệt riêng biệt
- Tự động sử dụng proxy khác nhau cho mỗi tài khoản
- Nhận thưởng nhiều lần với các tài khoản khác nhau
- Xử lý vấn đề timeout và website không truy cập được

## 🚀 Cách sử dụng

### Bước 1: <PERSON><PERSON><PERSON> bị
1. Khởi động Chrome Proxy Tool
2. Đợi tool tải danh sách proxy (tự động)
3. Kiểm tra kết nối internet

### Bước 2: Cấu hình đăng ký hàng loạt
1. **<PERSON><PERSON> tài khoản**: <PERSON>ọ<PERSON> từ 1-100 (khu<PERSON>ến nghị: 5-20 cho lần đầu)
2. **<PERSON><PERSON><PERSON> thời**: <PERSON><PERSON> trình duyệt chạy cùng lúc (1-5, khuyến nghị: 2-3)
3. **Chế độ ẩn**: ✅ Bật để tiết kiệm tài nguyên
4. **Họ tên**: Mặc định "TRAN HOANG AN" (áp dụng cho tất cả)

### Bước 3: Thực hiện
1. Nhấn "🏭 Đăng ký Hàng loạt"
2. Xác nhận thông tin trong popup
3. Theo dõi tiến độ trong log
4. Chờ hoàn thành (2-5 phút/tài khoản)

### Bước 4: Kết quả
- Popup hiển thị danh sách tài khoản đã tạo
- File `accounts_YYYYMMDD_HHMMSS.txt` chứa thông tin chi tiết
- Log hiển thị thống kê thành công/thất bại

## 🔧 Cách hoạt động

### 1. Chuẩn bị proxy và URL
```
- Tải 50+ proxy từ nhiều nguồn
- Tìm URL hoạt động thay thế (13win.com, 13win.net, etc.)
- Test kết nối trước khi bắt đầu
```

### 2. Tạo trình duyệt riêng biệt
```
Mỗi tài khoản:
- Chrome profile riêng biệt
- Proxy khác nhau
- User agent ngẫu nhiên
- Cookies/session độc lập
```

### 3. Đăng ký tự động
```
- Truy cập website với URL tốt nhất
- Tự động điền form đăng ký
- Username: user{số}_{random}
- Password: pass{random}
- Submit và kiểm tra kết quả
```

### 4. Quản lý tài nguyên
```
- Đóng trình duyệt sau khi hoàn thành
- Xóa profile để tiết kiệm dung lượng
- Giải phóng proxy cho tài khoản tiếp theo
```

## ⚙️ Tùy chọn nâng cao

### 📊 Số tài khoản
- **1-5**: Test nhanh, ít tài nguyên
- **5-20**: Sử dụng thường xuyên
- **20-50**: Chạy hàng loạt lớn
- **50-100**: Chỉ khi có máy mạnh

### ⚡ Đồng thời
- **1**: Chậm nhưng ổn định nhất
- **2-3**: Cân bằng tốc độ và ổn định (khuyến nghị)
- **4-5**: Nhanh nhưng tốn tài nguyên

### 🖥️ Chế độ hiển thị
- **Chế độ ẩn (Headless)**: 
  - ✅ Tiết kiệm RAM và CPU
  - ✅ Chạy nhanh hơn
  - ❌ Không thể theo dõi trực quan
  
- **Chế độ hiển thị**:
  - ✅ Theo dõi được quá trình
  - ✅ Debug dễ dàng
  - ❌ Tốn nhiều tài nguyên

## 🛡️ Xử lý sự cố tự động

### 🌐 Vấn đề timeout website
**Giải pháp tự động**:
- Tìm URL thay thế (13win.com, 13win.net, etc.)
- Thử nhiều domain khác nhau
- Sử dụng HTTP thay vì HTTPS
- Test với proxy khác nhau

### 🔄 Proxy không hoạt động
**Giải pháp tự động**:
- Tự động chuyển proxy khác
- Test proxy trước khi sử dụng
- Reload danh sách proxy khi hết
- Fallback về kết nối trực tiếp

### 💻 Trình duyệt bị lỗi
**Giải pháp tự động**:
- Tự động đóng và khởi động lại
- Tạo profile mới hoàn toàn
- Xóa cache và cookies
- Retry với cài đặt khác

## 📊 Thống kê và báo cáo

### 📈 Trong quá trình chạy
```
📦 Batch 1: Đăng ký tài khoản [1, 2, 3]
🚀 Bắt đầu đăng ký tài khoản #1
🌐 Sử dụng proxy: ***********:8080
🔗 Thử truy cập: https://13win.com/?id=*********
✅ Truy cập thành công: https://13win.com/register
🎉 Tài khoản #1 đăng ký thành công!
📊 Tiến độ: 3/10 - Thành công: 2 - Thất bại: 1
```

### 📋 Kết quả cuối cùng
```
🏁 HOÀN THÀNH ĐĂNG KÝ HÀNG LOẠT!
📊 Tổng kết: 8/10 tài khoản thành công
✅ Thành công: 8
❌ Thất bại: 2
💾 Đã lưu danh sách tài khoản vào: accounts_20241129_143022.txt
```

### 📁 File kết quả
```
DANH SÁCH TÀI KHOẢN ĐÃ TẠO
==================================================
Thời gian: 2024-11-29 14:30:22
Tổng số: 8 tài khoản

Tài khoản #1:
  Username: user1_4567
  Password: pass89012
  Họ tên: TRAN HOANG AN
  Proxy: ***********:8080
  URL: https://13win.com/dashboard
  Thời gian: 14:30:45
------------------------------
...
```

## 💡 Tips tối ưu

### 🎯 Tăng tỷ lệ thành công
- Chạy test với 2-3 tài khoản trước
- Kiểm tra website có hoạt động không
- Sử dụng proxy chất lượng cao
- Chọn thời gian ít tải (sáng sớm, đêm khuya)

### ⚡ Tăng tốc độ
- Bật chế độ ẩn (Headless)
- Giảm số trình duyệt đồng thời nếu máy yếu
- Đóng các ứng dụng khác
- Sử dụng SSD thay vì HDD

### 🔒 Tăng tính ẩn danh
- Mỗi tài khoản dùng proxy khác nhau
- Delay ngẫu nhiên giữa các tài khoản
- Không chạy quá nhiều cùng lúc
- Thay đổi thời gian chạy

### 💾 Quản lý tài khoản
- Lưu file kết quả an toàn
- Backup danh sách tài khoản
- Ghi chú proxy tốt để dùng lại
- Test login thử nghiệm

## 🚨 Lưu ý quan trọng

### ✅ Nên làm
- Test với số lượng nhỏ trước (2-5 tài khoản)
- Kiểm tra kết nối internet ổn định
- Đảm bảo máy có đủ RAM (8GB+ khuyến nghị)
- Chạy vào thời gian ít tải
- Backup kết quả thường xuyên

### ❌ Không nên
- Chạy quá 100 tài khoản cùng lúc
- Sử dụng cùng proxy cho nhiều tài khoản
- Chạy liên tục 24/7
- Bỏ qua kiểm tra kết quả
- Sử dụng thông tin cá nhân thật

### ⚠️ Rủi ro
- Website có thể phát hiện và chặn
- Proxy miễn phí không ổn định
- Tốn nhiều tài nguyên máy tính
- Có thể vi phạm điều khoản website

## 🔧 Xử lý sự cố

### ❌ Tất cả tài khoản đều thất bại
**Nguyên nhân**: Website down hoặc proxy không hoạt động
**Giải pháp**:
1. Kiểm tra website thủ công
2. Test kết nối với "🔍 Test Kết nối"
3. Thử chế độ "Không proxy"
4. Đợi và thử lại sau

### ❌ Một số tài khoản thành công, một số thất bại
**Nguyên nhân**: Bình thường, proxy không ổn định
**Giải pháp**:
1. Chạy lại với số lượng nhỏ hơn
2. Giảm số trình duyệt đồng thời
3. Kiểm tra file kết quả để lấy tài khoản thành công

### ❌ Máy tính bị lag/đơ
**Nguyên nhân**: Quá tải tài nguyên
**Giải pháp**:
1. Giảm số trình duyệt đồng thời
2. Bật chế độ ẩn (Headless)
3. Đóng các ứng dụng khác
4. Nâng cấp RAM nếu cần

### ❌ Lỗi "ChromeDriver"
**Nguyên nhân**: Driver không tương thích
**Giải pháp**:
1. Khởi động lại tool
2. Xóa thư mục `.chrome_driver`
3. Tool sẽ tự động tải driver mới

## 📞 Hỗ trợ

### 📁 File log
- `chrome_proxy_gui.log`: Log chi tiết
- `accounts_*.txt`: Danh sách tài khoản
- Kiểm tra log khi có lỗi

### 🔍 Debug
- Tắt chế độ ẩn để xem trình duyệt
- Giảm số lượng xuống 1 để test
- Kiểm tra từng bước thủ công

**Lưu ý**: Tính năng này chỉ dành cho mục đích học tập và test. Hãy tuân thủ điều khoản sử dụng của website và sử dụng có trách nhiệm.
