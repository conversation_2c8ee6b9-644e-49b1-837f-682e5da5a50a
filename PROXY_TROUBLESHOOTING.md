# 🔧 HƯỚNG DẪN XỬ LÝ SỰ CỐ PROXY

## 🚨 C<PERSON><PERSON> vấn đề thường gặp với Proxy

### 1. ❌ Proxy không kết nối được
**Triệu chứng:**
- Lỗi "net::ERR_PROXY_CONNECTION_FAILED"
- Timeout khi truy cập website
- Trang web không load

**Nguyên nhân:**
- Proxy server đã offline
- Proxy bị chặn bởi ISP
- Proxy quá chậm hoặc quá tải

**Giải pháp:**
1. **Thử proxy khác:**
   - Chọn proxy khác trong danh sách
   - Nhấn "Kiểm tra Proxy" trước khi sử dụng
   - Ưu tiên proxy có tốc độ "Fast"

2. **Làm mới danh sách proxy:**
   - Nhấn "Làm mới Proxy"
   - Đợi tool tải proxy mới
   - Chọn proxy từ danh sách mới

3. **Sử dụng chế độ "Không proxy":**
   - <PERSON><PERSON>n radio button "Không proxy"
   - Khởi động lại Chrome
   - T<PERSON>y cập trực tiếp

### 2. 🐌 Proxy quá chậm
**Triệu chứng:**
- Website load rất lâu
- Timeout thường xuyên
- Tốc độ hiển thị > 10s

**Giải pháp:**
1. **Chọn proxy nhanh hơn:**
   - Kiểm tra cột "Tốc độ" trong danh sách
   - Chọn proxy có tốc độ < 5s
   - Tránh proxy có status "Slow"

2. **Tối ưu cài đặt:**
   - Bật "Tắt hình ảnh" để tăng tốc
   - Sử dụng "Chế độ ẩn" để giảm tải
   - Giảm timeout trong code

### 3. 🔒 Proxy bị chặn
**Triệu chứng:**
- Một số website không truy cập được
- Lỗi "Access Denied" hoặc "Forbidden"
- Redirect về trang lỗi

**Giải pháp:**
1. **Thử proxy từ quốc gia khác:**
   - Proxy từ các nước khác nhau có thể truy cập khác nhau
   - Thử nhiều proxy khác nhau

2. **Sử dụng proxy thủ công:**
   - Tìm proxy premium hoặc private
   - Nhập vào ô "Proxy thủ công"
   - Format: `ip:port`

### 4. 🔄 Proxy không ổn định
**Triệu chứng:**
- Kết nối bị ngắt giữa chừng
- Phải refresh nhiều lần
- Proxy hoạt động rồi lại không hoạt động

**Giải pháp:**
1. **Sử dụng tính năng auto-retry:**
   - Tool sẽ tự động thử lại khi lỗi
   - Chờ tool tự động chuyển proxy

2. **Backup plan:**
   - Luôn có sẵn chế độ "Không proxy"
   - Chuẩn bị nhiều proxy khác nhau

## 🛠️ Các công cụ hỗ trợ trong Tool

### 🔍 Test Kết nối
**Cách sử dụng:**
1. Nhấn nút "🔍 Test Kết nối"
2. Tool sẽ test:
   - Kết nối internet trực tiếp
   - Kết nối qua proxy (nếu có)
   - Tốc độ kết nối

**Kết quả:**
- ✅ OK: Kết nối tốt
- ❌ Lỗi: Có vấn đề cần xử lý
- Thời gian phản hồi

### ✅ Kiểm tra Proxy
**Cách sử dụng:**
1. Chọn proxy trong danh sách
2. Nhấn "Kiểm tra Proxy"
3. Xem kết quả trong cột "Trạng thái"

**Các trạng thái:**
- **Fast (< 5s)**: Proxy tốt, nên sử dụng
- **Medium (5-10s)**: Proxy chấp nhận được
- **Slow (> 10s)**: Proxy chậm, tránh sử dụng
- **Connection failed**: Proxy không hoạt động
- **Error**: Có lỗi xảy ra

### 🔄 Làm mới Proxy
**Khi nào sử dụng:**
- Khi tất cả proxy đều không hoạt động
- Khi muốn có proxy mới
- Định kỳ mỗi 30 phút

**Lưu ý:**
- Quá trình có thể mất 30-60 giây
- Tool sẽ tự động test proxy mới
- Chỉ giữ lại proxy hoạt động

## 📋 Quy trình xử lý sự cố chuẩn

### Bước 1: Chẩn đoán
1. Nhấn "🔍 Test Kết nối"
2. Kiểm tra kết quả:
   - Internet OK + Proxy Lỗi → Vấn đề proxy
   - Internet Lỗi → Vấn đề mạng
   - Cả hai OK → Vấn đề khác

### Bước 2: Thử proxy khác
1. Chọn proxy khác trong danh sách
2. Ưu tiên proxy "Fast"
3. Test proxy trước khi sử dụng

### Bước 3: Làm mới proxy
1. Nhấn "Làm mới Proxy"
2. Đợi tải xong
3. Chọn proxy mới

### Bước 4: Fallback
1. Chuyển sang "Không proxy"
2. Khởi động lại Chrome
3. Truy cập trực tiếp

### Bước 5: Kiểm tra lại
1. Test kết nối sau mỗi thay đổi
2. Xác nhận website load được
3. Ghi nhớ proxy tốt để dùng sau

## 💡 Mẹo tối ưu Proxy

### 🎯 Chọn proxy tốt:
- **Tốc độ < 5s**: Ưu tiên hàng đầu
- **IP rõ ràng**: Tránh proxy có IP "Unknown"
- **Status "Working"**: Đã được test thành công

### ⚡ Tăng tốc độ:
- Bật "Tắt hình ảnh"
- Sử dụng "Chế độ ẩn"
- Đóng các tab không cần thiết

### 🔒 Tăng tính ổn định:
- Test proxy trước khi sử dụng
- Có backup proxy
- Sẵn sàng chuyển về "Không proxy"

### 📊 Theo dõi hiệu suất:
- Quan sát log để biết proxy nào tốt
- Ghi nhớ proxy hoạt động ổn định
- Tránh proxy thường xuyên lỗi

## 🆘 Khi nào nên dùng "Không proxy"

### ✅ Nên dùng khi:
- Tất cả proxy đều không hoạt động
- Cần truy cập nhanh và ổn định
- Website không yêu cầu ẩn IP
- Mạng internet ổn định

### ❌ Không nên dùng khi:
- Cần ẩn IP thật
- Website chặn IP của bạn
- Cần bypass geo-blocking
- Yêu cầu tính ẩn danh

## 📞 Hỗ trợ thêm

### 📁 File log:
- `chrome_proxy_gui.log`: Log chi tiết
- Kiểm tra log để tìm lỗi cụ thể

### 🔍 Debug:
- Bật Developer Tools trong Chrome
- Kiểm tra Network tab
- Xem Console để tìm lỗi JavaScript

### 🔄 Reset tool:
- Đóng hoàn toàn ứng dụng
- Xóa file log cũ
- Khởi động lại tool

**Lưu ý:** Proxy miễn phí luôn có độ tin cậy thấp. Để có trải nghiệm tốt nhất, hãy cân nhắc sử dụng proxy trả phí hoặc VPN chuyên nghiệp.
