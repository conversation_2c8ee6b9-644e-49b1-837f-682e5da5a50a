#!/usr/bin/env python3
"""
Mass Registration Tool
Tự động tạo nhiều tài kho<PERSON>n với các trình du<PERSON> kh<PERSON>c nhau
"""

import time
import random
import threading
import os
import shutil
from pathlib import Path
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from auto_registration import AutoRegistration
from simple_chrome_tool import download_chromedriver_137
from proxy_manager import ProxyManager
from url_resolver import URLResolver

logger = logging.getLogger(__name__)

class MassRegistration:
    def __init__(self, callback=None):
        self.callback = callback  # Callback để update GUI
        self.proxy_manager = ProxyManager()
        self.url_resolver = URLResolver()
        self.working_proxies = []
        self.working_urls = []
        self.registration_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.is_running = False
        self.accounts_created = []

    def log(self, message):
        """Log message và gửi về GUI"""
        logger.info(message)
        if self.callback:
            self.callback(message)

    def get_fresh_proxy(self):
        """Lấy proxy mới cho mỗi lần đăng ký"""
        if not self.working_proxies:
            self.log("🔄 Đang tải danh sách proxy mới...")
            self.working_proxies = self.proxy_manager.get_working_proxies(50)

        if self.working_proxies:
            proxy = random.choice(self.working_proxies)
            # Xóa proxy đã dùng để tránh trùng lặp
            self.working_proxies.remove(proxy)
            return proxy
        return None

    def create_fresh_profile(self, profile_name):
        """Tạo Chrome profile mới hoàn toàn sạch"""
        profile_dir = Path.home() / '.chrome_profiles' / profile_name

        # Xóa profile cũ nếu có
        if profile_dir.exists():
            try:
                shutil.rmtree(profile_dir)
            except Exception as e:
                self.log(f"⚠️ Không thể xóa profile cũ: {e}")

        # Tạo thư mục mới
        profile_dir.mkdir(parents=True, exist_ok=True)
        return str(profile_dir)

    def setup_chrome_options(self, proxy=None, profile_dir=None, headless=False):
        """Thiết lập Chrome options với profile và proxy riêng"""
        options = Options()

        # Basic options
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # Headless mode
        if headless:
            options.add_argument("--headless")

        # Profile riêng
        if profile_dir:
            options.add_argument(f"--user-data-dir={profile_dir}")
            options.add_argument("--profile-directory=Default")

        # Proxy
        if proxy:
            options.add_argument(f'--proxy-server=http://{proxy}')
            self.log(f"🌐 Sử dụng proxy: {proxy}")

        # Tăng tính ẩn danh
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")
        options.add_argument("--disable-javascript")  # Tắt JS để tăng tốc
        options.add_argument("--disable-extensions")

        # Random user agent
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        options.add_argument(f"--user-agent={random.choice(user_agents)}")

        return options

    def get_working_url(self, base_url, proxy=None):
        """Lấy URL hoạt động từ URL resolver"""
        if not self.working_urls:
            self.log("🔍 Đang tìm URL hoạt động...")
            working_urls = self.url_resolver.find_working_urls(base_url, max_urls=5, use_proxy=proxy)
            self.working_urls = [url_info['url'] for url_info in working_urls]

            if self.working_urls:
                self.log(f"✅ Tìm thấy {len(self.working_urls)} URL hoạt động")
            else:
                self.log("❌ Không tìm thấy URL nào hoạt động")

        if self.working_urls:
            # Trả về URL ngẫu nhiên từ danh sách
            return random.choice(self.working_urls)

        return base_url  # Fallback về URL gốc

    def try_multiple_urls(self, driver, base_url):
        """Thử nhiều URL khác nhau để tránh timeout"""
        # Lấy URL từ resolver
        working_url = self.get_working_url(base_url)

        urls_to_try = [working_url]

        # Thêm các URL từ working_urls
        if self.working_urls:
            urls_to_try.extend(self.working_urls[:3])

        # Thêm một số URL backup
        urls_to_try.extend([
            base_url,
            base_url.replace("www.", ""),
            base_url.replace("13win16", "13win"),
            "https://13win.com/?id=*********",
            "https://13win.net/?id=*********",
        ])

        # Loại bỏ trùng lặp
        urls_to_try = list(set(urls_to_try))

        for url in urls_to_try:
            try:
                self.log(f"🔗 Thử truy cập: {url}")
                driver.set_page_load_timeout(15)  # Timeout ngắn hơn
                driver.get(url)

                # Kiểm tra trang có load được không
                time.sleep(2)
                current_title = driver.title.lower()
                current_url = driver.current_url.lower()

                # Kiểm tra các dấu hiệu trang đúng
                success_indicators = ['13win', 'casino', 'game', 'đăng ký', 'register', 'login']
                if any(indicator in current_title or indicator in current_url for indicator in success_indicators):
                    self.log(f"✅ Truy cập thành công: {driver.current_url}")
                    return True

            except Exception as e:
                self.log(f"❌ Lỗi truy cập {url}: {str(e)[:50]}...")
                continue

        return False

    def single_registration(self, account_number, target_url, headless=True):
        """Đăng ký một tài khoản với trình duyệt riêng"""
        driver = None
        try:
            self.log(f"🚀 Bắt đầu đăng ký tài khoản #{account_number}")

            # Lấy proxy mới
            proxy = self.get_fresh_proxy()
            if not proxy:
                self.log("❌ Không có proxy khả dụng")
                return False

            # Tạo profile mới
            profile_name = f"account_{account_number}_{int(time.time())}"
            profile_dir = self.create_fresh_profile(profile_name)

            # Setup Chrome
            chrome_options = self.setup_chrome_options(proxy, profile_dir, headless)

            # Khởi động Chrome
            driver_path = download_chromedriver_137()
            if not driver_path:
                self.log("❌ Không thể tải ChromeDriver")
                return False

            service = Service(driver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Ẩn automation indicators
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # Thử truy cập website
            if not self.try_multiple_urls(driver, target_url):
                self.log(f"❌ Không thể truy cập website cho tài khoản #{account_number}")
                return False

            # Chờ trang load hoàn toàn
            time.sleep(random.uniform(3, 7))

            # Tự động đăng ký
            auto_reg = AutoRegistration(driver)
            username = f"user{account_number}_{random.randint(1000, 9999)}"
            password = f"pass{random.randint(10000, 99999)}"
            fullname = "TRAN HOANG AN"

            success = auto_reg.auto_register(username, password, fullname)

            if success:
                self.success_count += 1
                account_info = {
                    'number': account_number,
                    'username': username,
                    'password': password,
                    'fullname': fullname,
                    'proxy': proxy,
                    'url': driver.current_url,
                    'time': time.strftime("%H:%M:%S")
                }
                self.accounts_created.append(account_info)

                self.log(f"🎉 Tài khoản #{account_number} đăng ký thành công!")
                self.log(f"   👤 Username: {username}")
                self.log(f"   🔑 Password: {password}")

                # Chờ một chút để nhận thưởng
                time.sleep(random.uniform(5, 10))

                return True
            else:
                self.failed_count += 1
                self.log(f"❌ Tài khoản #{account_number} đăng ký thất bại")
                return False

        except Exception as e:
            self.failed_count += 1
            self.log(f"❌ Lỗi đăng ký tài khoản #{account_number}: {str(e)}")
            return False
        finally:
            # Đóng browser
            if driver:
                try:
                    driver.quit()
                except:
                    pass

            # Xóa profile để tiết kiệm dung lượng
            try:
                if 'profile_dir' in locals():
                    shutil.rmtree(profile_dir, ignore_errors=True)
            except:
                pass

    def mass_register(self, num_accounts, target_url, max_concurrent=3, headless=True):
        """Đăng ký hàng loạt nhiều tài khoản"""
        self.log(f"🎯 Bắt đầu đăng ký {num_accounts} tài khoản")
        self.log(f"🔧 Chế độ: {'Headless' if headless else 'Hiển thị'}")
        self.log(f"⚡ Đồng thời tối đa: {max_concurrent} trình duyệt")

        self.is_running = True
        self.registration_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.accounts_created = []

        # Tải proxy trước
        self.log("🔄 Đang chuẩn bị proxy...")
        self.working_proxies = self.proxy_manager.get_working_proxies(num_accounts * 2)
        self.log(f"✅ Đã chuẩn bị {len(self.working_proxies)} proxy")

        # Đăng ký từng batch
        for batch_start in range(0, num_accounts, max_concurrent):
            if not self.is_running:
                break

            batch_end = min(batch_start + max_concurrent, num_accounts)
            batch_accounts = list(range(batch_start + 1, batch_end + 1))

            self.log(f"📦 Batch {batch_start//max_concurrent + 1}: Đăng ký tài khoản {batch_accounts}")

            # Tạo threads cho batch này
            threads = []
            for account_num in batch_accounts:
                thread = threading.Thread(
                    target=self.single_registration,
                    args=(account_num, target_url, headless),
                    daemon=True
                )
                threads.append(thread)
                thread.start()

                # Delay nhỏ giữa các thread
                time.sleep(random.uniform(1, 3))

            # Chờ batch hoàn thành
            for thread in threads:
                thread.join()

            self.registration_count = batch_end
            self.log(f"📊 Tiến độ: {self.registration_count}/{num_accounts} - Thành công: {self.success_count} - Thất bại: {self.failed_count}")

            # Nghỉ giữa các batch
            if batch_end < num_accounts:
                delay = random.uniform(10, 20)
                self.log(f"⏸️ Nghỉ {delay:.1f}s trước batch tiếp theo...")
                time.sleep(delay)

        # Báo cáo kết quả
        self.log("🏁 HOÀN THÀNH ĐĂNG KÝ HÀNG LOẠT!")
        self.log(f"📊 Tổng kết: {self.success_count}/{num_accounts} tài khoản thành công")
        self.log(f"✅ Thành công: {self.success_count}")
        self.log(f"❌ Thất bại: {self.failed_count}")

        # Lưu danh sách tài khoản
        self.save_accounts_to_file()

        self.is_running = False
        return self.accounts_created

    def save_accounts_to_file(self):
        """Lưu danh sách tài khoản vào file"""
        if not self.accounts_created:
            return

        filename = f"accounts_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("DANH SÁCH TÀI KHOẢN ĐÃ TẠO\n")
                f.write("=" * 50 + "\n")
                f.write(f"Thời gian: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Tổng số: {len(self.accounts_created)} tài khoản\n\n")

                for account in self.accounts_created:
                    f.write(f"Tài khoản #{account['number']}:\n")
                    f.write(f"  Username: {account['username']}\n")
                    f.write(f"  Password: {account['password']}\n")
                    f.write(f"  Họ tên: {account['fullname']}\n")
                    f.write(f"  Proxy: {account['proxy']}\n")
                    f.write(f"  URL: {account['url']}\n")
                    f.write(f"  Thời gian: {account['time']}\n")
                    f.write("-" * 30 + "\n")

            self.log(f"💾 Đã lưu danh sách tài khoản vào: {filename}")
        except Exception as e:
            self.log(f"❌ Lỗi lưu file: {e}")

    def stop(self):
        """Dừng quá trình đăng ký"""
        self.is_running = False
        self.log("⏹️ Đã dừng quá trình đăng ký hàng loạt")


def main():
    """Test mass registration"""
    logging.basicConfig(level=logging.INFO)

    mass_reg = MassRegistration()

    # Test với 3 tài khoản
    accounts = mass_reg.mass_register(
        num_accounts=3,
        target_url="https://www.13win16.com/?id=*********",
        max_concurrent=2,
        headless=True
    )

    print(f"\nĐã tạo {len(accounts)} tài khoản thành công!")
    for account in accounts:
        print(f"- {account['username']} / {account['password']}")


if __name__ == "__main__":
    main()
