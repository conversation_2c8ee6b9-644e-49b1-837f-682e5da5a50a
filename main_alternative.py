#!/usr/bin/env python3
"""
Chrome Proxy Tool - Alternative version with custom ChromeDriver manager
A tool to access websites using Chrome browser with proxy configuration.
"""

import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from chrome_driver_manager import ChromeDriverManager
from config import PROXY_CONFIG, TARGET_URL, CHROME_OPTIONS, TIMEOUTS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('chrome_proxy.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ChromeProxyTool:
    def __init__(self):
        self.driver = None
        self.setup_chrome_options()
    
    def setup_chrome_options(self):
        """Configure Chrome options including proxy settings"""
        self.chrome_options = Options()
        
        # Basic Chrome options
        if CHROME_OPTIONS['headless']:
            self.chrome_options.add_argument('--headless')
        
        self.chrome_options.add_argument(f"--window-size={CHROME_OPTIONS['window_size'][0]},{CHROME_OPTIONS['window_size'][1]}")
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        self.chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Custom user agent
        if CHROME_OPTIONS['user_agent']:
            self.chrome_options.add_argument(f"--user-agent={CHROME_OPTIONS['user_agent']}")
        
        # Disable images for faster loading
        if CHROME_OPTIONS['disable_images']:
            prefs = {"profile.managed_default_content_settings.images": 2}
            self.chrome_options.add_experimental_option("prefs", prefs)
        
        # Configure proxy
        self.setup_proxy()
    
    def setup_proxy(self):
        """Setup proxy configuration"""
        if PROXY_CONFIG['http_proxy'] or PROXY_CONFIG['https_proxy']:
            # HTTP/HTTPS proxy
            proxy_server = PROXY_CONFIG['http_proxy'] or PROXY_CONFIG['https_proxy']
            self.chrome_options.add_argument(f'--proxy-server={proxy_server}')
            logger.info(f"Using HTTP/HTTPS proxy: {proxy_server}")
            
        elif PROXY_CONFIG['socks_proxy']:
            # SOCKS proxy
            self.chrome_options.add_argument(f'--proxy-server={PROXY_CONFIG["socks_proxy"]}')
            logger.info(f"Using SOCKS proxy: {PROXY_CONFIG['socks_proxy']}")
        
        # Proxy authentication (if needed)
        if PROXY_CONFIG['username'] and PROXY_CONFIG['password']:
            # Note: Chrome doesn't support proxy auth via command line
            # This would require additional handling with extensions or other methods
            logger.warning("Proxy authentication detected but not implemented in this version")
    
    def start_browser(self):
        """Initialize and start Chrome browser"""
        try:
            # Get ChromeDriver using our custom manager
            logger.info("Getting ChromeDriver...")
            driver_manager = ChromeDriverManager()
            driver_path = driver_manager.get_driver_path()
            
            if not driver_path:
                logger.error("Failed to get ChromeDriver")
                return False
            
            logger.info(f"Using ChromeDriver at: {driver_path}")
            
            # Setup Chrome service
            service = Service(driver_path)
            
            # Create driver instance
            self.driver = webdriver.Chrome(service=service, options=self.chrome_options)
            
            # Set timeouts
            self.driver.set_page_load_timeout(TIMEOUTS['page_load'])
            self.driver.implicitly_wait(TIMEOUTS['implicit_wait'])
            
            # Execute script to hide automation indicators
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Chrome browser started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Chrome browser: {str(e)}")
            return False
    
    def navigate_to_website(self, url=None):
        """Navigate to the target website"""
        target = url or TARGET_URL
        
        try:
            logger.info(f"Navigating to: {target}")
            self.driver.get(target)
            
            # Wait for page to load
            WebDriverWait(self.driver, TIMEOUTS['page_load']).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info(f"Successfully loaded: {self.driver.current_url}")
            logger.info(f"Page title: {self.driver.title}")
            
            return True
            
        except TimeoutException:
            logger.error(f"Timeout while loading {target}")
            return False
        except WebDriverException as e:
            logger.error(f"WebDriver error while loading {target}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error while loading {target}: {str(e)}")
            return False
    
    def wait_for_user_interaction(self):
        """Keep browser open for user interaction"""
        try:
            logger.info("Browser is ready. Press Ctrl+C to close...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, closing browser...")
    
    def close_browser(self):
        """Close the browser"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Browser closed successfully")
            except Exception as e:
                logger.error(f"Error closing browser: {str(e)}")
    
    def run(self):
        """Main execution method"""
        logger.info("Starting Chrome Proxy Tool (Alternative version)...")
        
        # Start browser
        if not self.start_browser():
            logger.error("Failed to start browser. Exiting...")
            return False
        
        # Navigate to website
        if not self.navigate_to_website():
            logger.error("Failed to navigate to website. Closing browser...")
            self.close_browser()
            return False
        
        # Keep browser open for interaction
        try:
            self.wait_for_user_interaction()
        finally:
            self.close_browser()
        
        return True


def main():
    """Main entry point"""
    tool = ChromeProxyTool()
    
    try:
        success = tool.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        tool.close_browser()
        sys.exit(1)


if __name__ == "__main__":
    main()
