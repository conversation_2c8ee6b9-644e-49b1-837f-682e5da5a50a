# 📖 HƯỚNG DẪN SỬ DỤNG CHROME PROXY TOOL

## 🚀 Khởi chạy nhanh

### Cách 1: Sử dụng file batch (Windows)
```
Nhấp đúp vào file: start_tool.bat
```

### Cách 2: Sử dụng Python
```bash
python run_gui.py
```

## 🖥️ Giao diện chính

### 1. Website URL
- **Mục đích**: <PERSON>hập địa chỉ website muốn truy cập
- **Mặc định**: https://www.13win16.com/?id=391111507
- **<PERSON><PERSON> thể thay đổi**: Bất kỳ URL nào

### 2. Proxy Settings (Cài đặt Proxy)

#### 🔘 Chế độ Proxy:
- **Không proxy**: Truy cập trực tiếp, không qua proxy
- **Proxy tự động**: Sử dụng proxy từ danh sách miễn phí
- **Proxy thủ công**: Nhập proxy riêng của bạn

#### 📝 Proxy thủ công:
- **Format**: `ip:port`
- **Ví dụ**: `***********:8080`

#### 📊 Proxy hiện tại:
- Hiển thị proxy đang được sử dụng

### 3. Danh sách Proxy miễn phí

#### 🔄 Làm mới Proxy:
- Tải danh sách proxy mới từ internet
- Tự động kiểm tra và lọc proxy hoạt động

#### ✅ Kiểm tra Proxy:
- Chọn proxy trong danh sách
- Nhấn "Kiểm tra Proxy" để test

#### 📋 Bảng thông tin:
- **Proxy**: Địa chỉ IP:Port
- **Trạng thái**: Working/Not working/Đang kiểm tra
- **IP**: IP thực tế khi sử dụng proxy

### 4. Tùy chọn Browser

#### ☑️ Chế độ ẩn (Headless):
- ✅ Bật: Chrome chạy ngầm, không hiển thị cửa sổ
- ❌ Tắt: Chrome hiển thị bình thường

#### ☑️ Tắt hình ảnh:
- ✅ Bật: Không tải hình ảnh, tăng tốc độ
- ❌ Tắt: Tải đầy đủ hình ảnh

#### 🌐 User Agent:
- **Default**: Sử dụng User Agent mặc định
- **Windows**: Giả lập trình duyệt Windows
- **Mac**: Giả lập trình duyệt Mac
- **Linux**: Giả lập trình duyệt Linux

### 5. Điều khiển

#### 🚀 Khởi động Chrome:
- Bắt đầu trình duyệt Chrome với cài đặt đã chọn
- Tự động tải ChromeDriver phù hợp

#### ⏹️ Dừng Chrome:
- Đóng trình duyệt Chrome
- Giải phóng tài nguyên

#### 🌐 Truy cập Website:
- Điều hướng đến URL đã nhập
- Chỉ hoạt động khi Chrome đã khởi động

#### 🗑️ Xóa Log:
- Xóa toàn bộ log hiển thị

### 6. Trạng thái & Log
- **Trạng thái**: Hiển thị hoạt động hiện tại
- **Log**: Chi tiết các bước thực hiện

## 📋 Quy trình sử dụng chuẩn

### Bước 1: Chuẩn bị
1. Khởi chạy ứng dụng
2. Đợi tải danh sách proxy (tự động)

### Bước 2: Cấu hình
1. **Chọn chế độ proxy**:
   - Proxy tự động: Chọn proxy từ danh sách
   - Proxy thủ công: Nhập proxy của bạn
   - Không proxy: Bỏ qua bước này

2. **Tùy chọn browser** (tùy chọn):
   - Bật chế độ ẩn nếu không muốn hiển thị
   - Tắt hình ảnh để tăng tốc
   - Chọn User Agent phù hợp

### Bước 3: Khởi động
1. Nhấn "🚀 Khởi động Chrome"
2. Đợi Chrome khởi động thành công

### Bước 4: Truy cập
1. Kiểm tra/thay đổi URL nếu cần
2. Nhấn "🌐 Truy cập Website"
3. Đợi trang web tải xong

### Bước 5: Sử dụng
- Tương tác với website trong cửa sổ Chrome
- Theo dõi log để biết trạng thái

### Bước 6: Kết thúc
- Nhấn "⏹️ Dừng Chrome" khi hoàn thành
- Hoặc đóng ứng dụng

## 🔧 Xử lý sự cố

### ❌ Lỗi khởi động Chrome
**Nguyên nhân**: ChromeDriver không tương thích
**Giải pháp**: 
- Đóng Chrome đang chạy
- Khởi động lại ứng dụng
- Tool sẽ tự động tải driver phù hợp

### ❌ Proxy không hoạt động
**Nguyên nhân**: Proxy bị chặn hoặc chậm
**Giải pháp**:
- Thử proxy khác trong danh sách
- Nhấn "Làm mới Proxy" để tải proxy mới
- Sử dụng chế độ "Không proxy"

### ❌ Website không tải được
**Nguyên nhân**: Kết nối mạng hoặc website bị chặn
**Giải pháp**:
- Kiểm tra kết nối internet
- Thử proxy khác
- Kiểm tra URL có đúng không

### ❌ Ứng dụng bị đơ
**Giải pháp**:
- Đóng ứng dụng
- Mở Task Manager, kết thúc tiến trình Chrome
- Khởi động lại ứng dụng

## 💡 Mẹo sử dụng

### 🎯 Tối ưu tốc độ:
- Bật "Tắt hình ảnh"
- Sử dụng "Chế độ ẩn"
- Chọn proxy có ping thấp

### 🔒 Tăng tính ẩn danh:
- Sử dụng proxy từ nhiều quốc gia khác nhau
- Thay đổi User Agent thường xuyên
- Không đăng nhập tài khoản cá nhân

### 📊 Theo dõi hiệu suất:
- Quan sát log để biết proxy nào hoạt động tốt
- Ghi nhớ proxy tốt để sử dụng thủ công
- Kiểm tra IP thực tế sau khi kết nối

## 🆘 Hỗ trợ

### 📁 File log:
- `chrome_proxy_gui.log`: Log chi tiết
- `chrome_proxy.log`: Log của phiên bản console

### 🔍 Kiểm tra:
- Đảm bảo Python 3.7+ đã cài đặt
- Đảm bảo Chrome browser đã cài đặt
- Kiểm tra kết nối internet

### 📞 Liên hệ:
- Kiểm tra file README.md để biết thêm thông tin
- Xem log file để tìm lỗi cụ thể
