#!/usr/bin/env python3
"""
Khởi chạy Chrome Proxy Tool với GUI
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Kiểm tra các thư viện cần thiết"""
    required_modules = [
        'selenium',
        'requests',
        'bs4',  # beautifulsoup4
        'PIL',  # Pillow
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    return missing_modules

def install_dependencies():
    """Cài đặt các thư viện thiếu"""
    try:
        print("Đang cài đặt các thư viện cần thiết...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Cài đặt thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Lỗi cài đặt: {e}")
        return False

def main():
    """Hàm chính"""
    print("=" * 50)
    print("Chrome Proxy Tool - GUI Version")
    print("Tool truy cập web với proxy miễn phí")
    print("=" * 50)
    
    # Kiểm tra dependencies
    missing = check_dependencies()
    
    if missing:
        print(f"Thiếu các thư viện: {', '.join(missing)}")
        
        # Hỏi người dùng có muốn cài đặt không
        try:
            root = tk.Tk()
            root.withdraw()  # Ẩn cửa sổ chính
            
            result = messagebox.askyesno(
                "Thiếu thư viện",
                f"Thiếu các thư viện: {', '.join(missing)}\n\n"
                "Bạn có muốn cài đặt tự động không?"
            )
            
            root.destroy()
            
            if result:
                if install_dependencies():
                    print("Khởi động lại ứng dụng...")
                    # Khởi động lại
                    os.execv(sys.executable, [sys.executable] + sys.argv)
                else:
                    print("Không thể cài đặt thư viện. Vui lòng cài đặt thủ công:")
                    print("pip install -r requirements.txt")
                    return
            else:
                print("Hủy bỏ. Vui lòng cài đặt thủ công:")
                print("pip install -r requirements.txt")
                return
                
        except Exception as e:
            print(f"Lỗi: {e}")
            print("Vui lòng cài đặt thủ công: pip install -r requirements.txt")
            return
    
    # Khởi chạy GUI
    try:
        print("Đang khởi động GUI...")
        from gui_chrome_tool import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"Lỗi import: {e}")
        print("Vui lòng kiểm tra lại các file cần thiết:")
        print("- gui_chrome_tool.py")
        print("- proxy_manager.py")
        print("- simple_chrome_tool.py")
        
    except Exception as e:
        print(f"Lỗi khởi động: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
