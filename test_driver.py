#!/usr/bin/env python3
"""
Test script for ChromeDriver manager
"""

from chrome_driver_manager import ChromeDriverManager

def main():
    print("Testing ChromeDriver Manager...")
    
    try:
        manager = ChromeDriverManager()
        
        # Get Chrome version
        chrome_version = manager.get_chrome_version()
        print(f"Chrome version: {chrome_version}")
        
        if chrome_version:
            # Get compatible driver version
            driver_version = manager.get_compatible_driver_version(chrome_version)
            print(f"Compatible ChromeDriver version: {driver_version}")
            
            if driver_version:
                # Get driver path (this will download if needed)
                driver_path = manager.get_driver_path()
                print(f"ChromeDriver path: {driver_path}")
                
                if driver_path:
                    print("✓ ChromeDriver manager working correctly!")
                    return True
                else:
                    print("✗ Failed to get ChromeDriver path")
            else:
                print("✗ Failed to get compatible ChromeDriver version")
        else:
            print("✗ Failed to detect Chrome version")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
    
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\nTrying manual ChromeDriver download...")
        
        # Manual fallback
        try:
            manager = ChromeDriverManager()
            # Try downloading a specific version that should work with Chrome 137
            driver_path = manager.download_chromedriver("137.0.7151.55")
            if driver_path:
                print(f"✓ Manual download successful: {driver_path}")
            else:
                print("✗ Manual download failed")
        except Exception as e:
            print(f"✗ Manual download error: {e}")
